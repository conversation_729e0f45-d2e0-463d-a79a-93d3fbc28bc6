// backend/scripts/remove_CGP0001_from_filenames_and_db.js

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

const targetDir = path.join(__dirname, '../public/uploads/products');
const dbPath = path.join(__dirname, '../database/product.db');

const db = new sqlite3.Database(dbPath);

fs.readdir(targetDir, (err, files) => {
  if (err) {
    console.error('讀取目錄失敗:', err);
    return;
  }

  let count = 0;
  files.forEach(file => {
    if (file.includes('CGP0001_')) {
      const newName = file.replace('CGP0001_', '');
      const oldPath = path.join(targetDir, file);
      const newPath = path.join(targetDir, newName);

      // 檢查新檔名是否已存在，避免覆蓋
      if (fs.existsSync(newPath)) {
        console.warn(`檔案已存在，跳過：${newName}`);
        return;
      }

      // 1. 重新命名檔案
      fs.renameSync(oldPath, newPath);
      console.log(`已重新命名：${file} → ${newName}`);

      // 2. 更新資料庫 image_path 欄位
      const oldDbPath = `/uploads/products/${file}`;
      const newDbPath = `/uploads/products/${newName}`;
      db.run(
        'UPDATE product_image SET image_path = ? WHERE image_path = ?',
        [newDbPath, oldDbPath],
        function (err) {
          if (err) {
            console.error(`更新資料庫失敗：${oldDbPath} → ${newDbPath}`, err);
          } else if (this.changes > 0) {
            console.log(`資料庫已更新：${oldDbPath} → ${newDbPath}`);
          }
        }
      );
      count++;
    }
  });

  console.log(`完成，總共處理 ${count} 個檔案。`);
  db.close();
});